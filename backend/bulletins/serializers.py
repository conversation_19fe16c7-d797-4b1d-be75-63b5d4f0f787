from rest_framework import serializers
from .models import Bulletin, BulletinAttachment, BulletinHistory
from user.models import Member
from group_role.models import Group
from towers.models import Tower, Unit


class BulletinAttachmentSerializer(serializers.ModelSerializer):
    """
    Serializer for bulletin attachments
    """
    file_url = serializers.SerializerMethodField()

    class Meta:
        model = BulletinAttachment
        fields = ['id', 'file', 'file_url', 'file_name', 'file_type', 'file_size', 'created_at']

    def get_file_url(self, obj):
        """
        Get the full URL for the file
        """
        if obj.file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None


class BulletinHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for bulletin history with comments and approval workflow
    """
    edited_by_name = serializers.CharField(source='edited_by.full_name', read_only=True)

    class Meta:
        model = BulletinHistory
        fields = ['id', 'edited_by', 'edited_by_name', 'edited_at', 'changes', 'comment', 'action']


class BulletinSerializer(serializers.ModelSerializer):
    """
    Main serializer for bulletins
    """
    creator_name = serializers.CharField(source='creator.full_name', read_only=True)
    creator_is_admin = serializers.SerializerMethodField()
    group_name = serializers.CharField(read_only=True)
    member_name = serializers.CharField(read_only=True)
    attachments = BulletinAttachmentSerializer(many=True, read_only=True)
    history = BulletinHistorySerializer(many=True, read_only=True)
    
    # For targeting
    target_towers_data = serializers.SerializerMethodField()
    target_units_data = serializers.SerializerMethodField()
    
    # Write-only fields for creating/updating
    target_tower_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    target_unit_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    
    class Meta:
        model = Bulletin
        fields = [
            'id', 'title', 'description', 'creator', 'creator_name', 'creator_is_admin', 'post_as',
            'posted_group', 'posted_member', 'group_name', 'member_name',
            'priority', 'label', 'status', 'views', 'is_pinned', 'created_at', 'updated_at',
            'attachments', 'target_towers_data', 'target_units_data', 'history',
            'target_tower_ids', 'target_unit_ids'
        ]
        read_only_fields = ['id', 'creator', 'views', 'created_at', 'updated_at']

    def get_creator_is_admin(self, obj):
        """
        Determine if the bulletin creator is an admin/organizational member
        """
        if obj.creator:
            return obj.creator.is_org_member
        return False

    def get_target_towers_data(self, obj):
        """
        Get tower data for the bulletin
        """
        towers = obj.target_towers.all()
        return [{'id': tower.id, 'tower_name': tower.tower_name, 'tower_number': tower.tower_number} for tower in towers]

    def get_target_units_data(self, obj):
        """
        Get unit data for the bulletin
        """
        units = obj.target_units.all()
        return [{'id': unit.id, 'unit_name': unit.unit_name, 'tower_name': unit.floor.tower.tower_name} for unit in units]

    def create(self, validated_data):
        """
        Create a new bulletin with tower and unit targeting
        """
        # Extract targeting data
        target_tower_ids = validated_data.pop('target_tower_ids', [])
        target_unit_ids = validated_data.pop('target_unit_ids', [])
        
        # Handle post_as logic
        post_as = validated_data.get('post_as', 'creator')
        if post_as == 'group' and validated_data.get('posted_group'):
            validated_data['group_name'] = validated_data['posted_group'].group_name
        elif post_as == 'member' and validated_data.get('posted_member'):
            validated_data['member_name'] = validated_data['posted_member'].full_name
        
        # Create the bulletin
        bulletin = Bulletin.objects.create(**validated_data)
        
        # Set targeting relationships
        if target_tower_ids:
            towers = Tower.objects.filter(id__in=target_tower_ids)
            bulletin.target_towers.set(towers)
        
        if target_unit_ids:
            units = Unit.objects.filter(id__in=target_unit_ids)
            bulletin.target_units.set(units)
        
        return bulletin

    def update(self, instance, validated_data):
        """
        Update an existing bulletin
        """
        # Extract targeting data
        target_tower_ids = validated_data.pop('target_tower_ids', None)
        target_unit_ids = validated_data.pop('target_unit_ids', None)
        
        # Handle post_as logic
        post_as = validated_data.get('post_as', instance.post_as)
        if post_as == 'group' and validated_data.get('posted_group'):
            validated_data['group_name'] = validated_data['posted_group'].name
        elif post_as == 'member' and validated_data.get('posted_member'):
            validated_data['member_name'] = validated_data['posted_member'].full_name
        
        # Update the bulletin
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update targeting relationships if provided
        if target_tower_ids is not None:
            towers = Tower.objects.filter(id__in=target_tower_ids)
            instance.target_towers.set(towers)
        
        if target_unit_ids is not None:
            units = Unit.objects.filter(id__in=target_unit_ids)
            instance.target_units.set(units)
        
        return instance


class BulletinListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for bulletin lists
    """
    creator_name = serializers.CharField(source='creator.full_name', read_only=True)
    creator_is_admin = serializers.SerializerMethodField()
    attachments = BulletinAttachmentSerializer(many=True, read_only=True)
    attachment_count = serializers.SerializerMethodField()
    target_towers_count = serializers.SerializerMethodField()
    target_units_count = serializers.SerializerMethodField()
    target_towers_data = serializers.SerializerMethodField()
    target_units_data = serializers.SerializerMethodField()

    class Meta:
        model = Bulletin
        fields = [
            'id', 'title', 'description', 'creator_name', 'creator_is_admin', 'post_as',
            'priority', 'label', 'status', 'views', 'is_pinned', 'created_at',
            'attachments', 'attachment_count', 'target_towers_count', 'target_units_count',
            'target_towers_data', 'target_units_data'
        ]

    def get_creator_is_admin(self, obj):
        """
        Determine if the bulletin creator is an admin/organizational member
        """
        if obj.creator:
            return obj.creator.is_org_member
        return False

    def get_attachments(self, obj):
        """
        Get attachments with proper context for file URLs
        """
        attachments = obj.attachments.all()
        return BulletinAttachmentSerializer(
            attachments,
            many=True,
            context=self.context
        ).data

    def get_attachment_count(self, obj):
        return obj.attachments.count()
    
    def get_target_towers_count(self, obj):
        return obj.target_towers.count()
    
    def get_target_units_count(self, obj):
        return obj.target_units.count()

    def get_target_towers_data(self, obj):
        """
        Get tower data for the bulletin
        """
        towers = obj.target_towers.all()
        return [{'id': tower.id, 'name': tower.tower_name, 'tower_name': tower.tower_name, 'tower_number': tower.tower_number} for tower in towers]

    def get_target_units_data(self, obj):
        """
        Get simplified unit data for display
        """
        units = obj.target_units.all()[:5]  # Limit to first 5 for performance
        return [{'id': unit.id, 'unit_name': unit.unit_name, 'unit_number': unit.unit_name} for unit in units]
