import React from 'react';
import { FaTimes } from 'react-icons/fa';
import AnnouncementPreview from './AnnouncementPreview';

const AnnouncementHistoryModal = ({ isOpen, onClose, announcement, currentUser }) => {
  if (!isOpen || !announcement) return null;

  // Get real history data from announcement
  const getAnnouncementHistory = (announcement) => {
    const history = [];

    // Get edit history from announcement.editHistory array (show edits first)
    if (announcement.editHistory && Array.isArray(announcement.editHistory)) {
      announcement.editHistory.forEach((edit, index) => {
        const editDate = new Date(edit.timestamp);
        history.push({
          id: `edit-${edit.timestamp}-${index}`,
          type: 'Edit',
          action: 'Edited by',
          user: edit.editedBy || 'Unknown User',
          timestamp: edit.timestamp,
          date: (() => {
            const day = editDate.getDate().toString().padStart(2, '0');
            const month = (editDate.getMonth() + 1).toString().padStart(2, '0');
            const year = editDate.getFullYear();
            const hours = editDate.getHours();
            const minutes = editDate.getMinutes().toString().padStart(2, '0');
            const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
            const period = hours >= 12 ? 'pm' : 'am';
            return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
          })(),
          changes: edit.changes || {}
        });
      });
    }

    // Also check for backend history format (fallback)
    if (announcement.history && Array.isArray(announcement.history)) {
      announcement.history.forEach((edit, index) => {
        const editDate = new Date(edit.edited_at);
        history.push({
          id: `edit-${edit.edited_at}-${index}`,
          type: 'Edit',
          action: 'Edited by',
          user: edit.edited_by_name || 'Unknown User',
          timestamp: edit.edited_at,
          date: (() => {
            const day = editDate.getDate().toString().padStart(2, '0');
            const month = (editDate.getMonth() + 1).toString().padStart(2, '0');
            const year = editDate.getFullYear();
            const hours = editDate.getHours();
            const minutes = editDate.getMinutes().toString().padStart(2, '0');
            const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
            const period = hours >= 12 ? 'pm' : 'am';
            return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
          })(),
          changes: edit.changes || {}
        });
      });
    }

    // Creation entry - add at the end (bottom of timeline)
    if (announcement.createdAt) {
      const createdDate = new Date(announcement.createdAt);
      history.push({
        id: `creation-${announcement.id}`,
        type: 'Creation',
        action: 'Created by',
        user: announcement.author || announcement.creatorName || 'Unknown User',
        timestamp: announcement.createdAt,
        date: (() => {
          const day = createdDate.getDate().toString().padStart(2, '0');
          const month = (createdDate.getMonth() + 1).toString().padStart(2, '0');
          const year = createdDate.getFullYear();
          const hours = createdDate.getHours();
          const minutes = createdDate.getMinutes().toString().padStart(2, '0');
          const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
          const period = hours >= 12 ? 'pm' : 'am';
          return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
        })(),
        color: '#3D9D9B'
      });
    }

    return history.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)); // Show latest first (descending)
  };

  const history = getAnnouncementHistory(announcement);

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Handle escape key
  React.useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div
        className="bg-white relative flex overflow-hidden w-[796px] h-[660px] max-w-[90vw] max-h-[90vh] rounded-[27px]"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 w-8 h-8 bg-primary hover:bg-[#2A7A78] rounded-full flex items-center justify-center text-white z-10 transition-colors"
        >
          <FaTimes className="w-4 h-4" />
        </button>

        {/* Left Side - Preview */}
        <div className="w-1/2 p-6 border-2 border-gray-200 overflow-y-auto">
          <div className="h-full">
            <AnnouncementPreview
              data={{
                title: announcement.title,
                description: announcement.description,
                authorName: announcement.author || announcement.creatorName,
                postAs: announcement.postAs || 'Creator',
                priority: announcement.priority,
                label: announcement.label,
                startDate: announcement.startDate,
                startTime: announcement.startTime,
                endDate: announcement.endDate,
                endTime: announcement.endTime,
                attachments: announcement.attachments ? announcement.attachments.map(att => ({
                  preview: att.file_url || att.url || att.preview || att,
                  name: att.file_name || att.name || 'Attachment',
                  url: att.file_url || att.url || att.preview || att,
                  type: att.file_type || att.type
                })) : [],
                labels: announcement.labels || [],
                towers: announcement.towers || [],
                units: announcement.units || [],
                // Add selectedUnits for user count calculation
                selectedUnits: announcement.target_units_data?.map(unit => unit.id) || [],
                // Also pass the full announcement object for compatibility
                target_units_data: announcement.target_units_data || []
              }}
              currentUser={currentUser}
              isInModal={true}
            />
          </div>
        </div>

        {/* Right Side - History */}
        <div className="w-1/2 p-6 overflow-y-auto">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-1">History</h2>
          </div>

          {/* History Timeline */}
          <div className="relative">
            {/* Continuous Timeline Line */}
            {history.length > 1 && (
              <div
                className="absolute left-[6px] top-4 w-0.5 bg-primary"
                style={{ height: `${(history.length - 1) * 96}px` }}
              ></div>
            )}

            <div className="space-y-6">
              {history.length > 0 ? (
                history.map((entry) => (
                  <div key={entry.id} className="flex items-start space-x-4 relative">
                    {/* Timeline Dot */}
                    <div className="relative z-10">
                      <div className="w-3 h-3 bg-primary rounded-full mt-1"></div>
                    </div>

                    {/* History Entry */}
                    <div className="flex-1 pb-2">
                      {/* Header with Type and Date */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="text-base font-medium text-primary">
                          {entry.type}
                        </div>
                        <div className="text-sm text-gray-500">
                          {entry.date}
                        </div>
                      </div>

                      {/* User Info */}
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-700">{entry.action}</span>
                        <span className="text-sm font-medium text-primary bg-gray-100 px-2 py-1 rounded radius-8">
                          {entry.user}
                        </span>
                      </div>


                    </div>
                  </div>
                ))
              ) : (
                <div className="text-gray-500 text-center py-4">No history available</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementHistoryModal;
