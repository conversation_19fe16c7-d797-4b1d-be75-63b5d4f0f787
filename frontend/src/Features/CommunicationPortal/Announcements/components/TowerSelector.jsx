import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, X } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { fetchTowers } from '../../../../redux/slices/api/announcementApi';

const TowerSelector = ({ value, onChange, placeholder = "Select Towers" }) => {
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState('bottom');
  const [towers, setTowers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const selectorRef = useRef(null);
  const inputRef = useRef(null);

  // Fetch towers from backend
  useEffect(() => {
    const loadTowers = async () => {
      try {
        setLoading(true);
        const result = await dispatch(fetchTowers());
        if (fetchTowers.fulfilled.match(result)) {
          setTowers(result.payload);
          setError(null);
        } else {
          setError('Failed to load towers');
          // Fallback to dummy data if API fails
          setTowers([
            { id: 1, tower_name: 'Tower 1', tower_number: 1 },
            { id: 2, tower_name: 'Tower 2', tower_number: 2 },
            { id: 3, tower_name: 'Tower 3', tower_number: 3 },
            { id: 4, tower_name: 'Tower 4', tower_number: 4 },
            { id: 5, tower_name: 'Tower 5', tower_number: 5 },
          ]);
        }
      } catch (err) {
        console.error('Error fetching towers:', err);
        setError('Failed to load towers');
        // Fallback to dummy data if API fails
        setTowers([
          { id: 1, tower_name: 'Tower 1', tower_number: 1 },
          { id: 2, tower_name: 'Tower 2', tower_number: 2 },
          { id: 3, tower_name: 'Tower 3', tower_number: 3 },
          { id: 4, tower_name: 'Tower 4', tower_number: 4 },
          { id: 5, tower_name: 'Tower 5', tower_number: 5 },
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadTowers();
  }, [dispatch]);

  // Create tower options with 'All' option
  const allTowers = towers.length > 0 ? ['All', ...towers.map(tower => tower.id)] : [];
  const regularTowers = towers.map(tower => tower.id);

  // Calculate dropdown position
  const calculatePosition = () => {
    if (inputRef.current) {
      const rect = inputRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      const dropdownHeight = 300;

      setDropdownPosition(spaceBelow < dropdownHeight && spaceAbove > spaceBelow ? 'top' : 'bottom');
    }
  };

  useEffect(() => {
    if (isOpen) {
      calculatePosition();
      window.addEventListener('scroll', calculatePosition);
      window.addEventListener('resize', calculatePosition);

      return () => {
        window.removeEventListener('scroll', calculatePosition);
        window.removeEventListener('resize', calculatePosition);
      };
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (selectorRef.current && !selectorRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  const handleTowerChange = (tower, isChecked) => {
    const currentValues = value || [];

    if (tower === 'All') {
      if (isChecked) {
        onChange(allTowers);
      } else {
        onChange([]);
      }
    } else {
      let newValues;
      if (isChecked) {
        newValues = [...currentValues, tower];
        if (regularTowers.every(t => newValues.includes(t))) {
          newValues = [...newValues, 'All'];
        }
      } else {
        newValues = currentValues.filter(v => v !== tower);
        newValues = newValues.filter(v => v !== 'All');
      }
      onChange(newValues);
    }
  };

  // Helper function to get tower name by ID
  const getTowerName = (towerId) => {
    if (towerId === 'All') return 'All Towers';
    const tower = towers.find(t => t.id === towerId);
    return tower ? tower.tower_name : `Tower ${towerId}`;
  };

  const handleClearAll = () => {
    onChange([]);
  };

  const getDisplayText = () => {
    if (!value || value.length === 0) {
      return placeholder;
    }
    if (value.includes('All')) {
      return 'All Towers Selected';
    }
    return `${value.length} Tower(s) Selected`;
  };

  const getSelectedDisplay = () => {
    if (!value || value.length === 0) {
      return null;
    }

    if (value.includes('All')) {
      return (
        <div className="mb-2 p-2 bg-primary bg-opacity-10 rounded-md border border-primary border-opacity-30">
          <div className="text-sm font-medium text-primary mb-1">Selected Towers:</div>
          <div className="text-sm text-gray-700">All Towers</div>
        </div>
      );
    }

    return (
      <div className="mb-2 p-2 bg-primary bg-opacity-10 rounded-md border border-primary border-opacity-30">
        <div className="text-sm font-medium text-primary mb-1">Selected Towers:</div>
        <div className="flex flex-wrap gap-1">
          {value.filter(tower => tower !== 'All').map((towerId) => (
            <span
              key={towerId}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary text-white"
            >
              {getTowerName(towerId)}
            </span>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="relative" ref={selectorRef}>
      {/* Selected Values Display */}
      {getSelectedDisplay()}

      {/* Input Field */}
      <div
        ref={inputRef}
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md bg-white cursor-pointer flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
      >
        <span className={value && value.length > 0 ? 'text-gray-900 text-sm' : 'text-gray-500 text-xs'}>
          {getDisplayText()}
        </span>
        <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </div>

      {/* Dropdown content */}
      {isOpen && (
        <div className={`absolute z-50 ${
          dropdownPosition === 'top' ? 'bottom-full mb-1' : 'top-full mt-1'
        } w-full bg-white border border-gray-300 rounded-md shadow-lg`}>
          <div className="max-h-60 overflow-y-auto p-3">
            {loading ? (
              <div className="p-2 text-gray-500 text-sm text-center">Loading towers...</div>
            ) : error ? (
              <div className="p-2 text-red-500 text-sm text-center">{error}</div>
            ) : allTowers.length === 0 ? (
              <div className="p-2 text-gray-500 text-sm text-center">No towers available</div>
            ) : (
              allTowers.map((tower) => (
                <label key={tower} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={value?.includes(tower) || false}
                      onChange={(e) => handleTowerChange(tower, e.target.checked)}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 border-2 rounded ${
                      value?.includes(tower)
                        ? 'bg-primary border-primary'
                        : 'border-gray-300 bg-white'
                    } flex items-center justify-center`}>
                      {value?.includes(tower) && (
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                  </div>
                  <span className="text-sm text-gray-700">{getTowerName(tower)}</span>
                </label>
              ))
            )}
          </div>
          <div className="border-t border-gray-200 p-3">
            <div className="flex gap-2">
              <button
                type="button"
                onClick={handleClearAll}
                className="flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors text-sm font-medium"
                title="Clear All"
              >
                Clear
              </button>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="flex-1 bg-primary text-white py-2 px-4 rounded-md hover:bg-[#34877A] transition-colors text-sm font-medium"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(TowerSelector);
