import { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { FaPlus, FaFilePdf, FaFileWord, FaImage } from "react-icons/fa";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>i<PERSON>ilter } from "react-icons/bi";
import BulletinHistoryModal from "../components/BulletinHistoryModal";
import usePinPost from "../components/PinPost";
import Calendar from "../../Announcements/components/Calendar";
import ConfirmationMessageBox from "../../../../Components/MessageBox/ConfirmationMessageBox";
import MessageBox from "../../../../Components/MessageBox/MessageBox";
import ImageSlider from "../../../../Components/Modal/ImageSlider";
import DocumentViewer from "../../../../Components/FileViewer/DocumentViewer";
import useBulletins from "../../../../hooks/useBulletins";
import BulletinListPreview from "./BulletinListPreview";
import BulletinTableView from "./BulletinTableView";
import FilterSelectModal from "../../../../Components/FilterSelect/FilterSelectModal";


const BulletinList = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Redux hooks for bulletins
  const {
    bulletins: reduxBulletins,
    loading: reduxLoading,
    deleteSuccess,
    message,
    loadBulletins: loadBulletinsRedux,
    removeBulletin: removeBulletinRedux,
    moveBulletinToArchive: moveBulletinToArchiveRedux,
    restoreArchivedBulletin: restoreArchivedBulletinRedux,
    loadBulletin: loadBulletinRedux,
    clearAllSuccess: clearAllSuccessRedux
  } = useBulletins();

  const bulletins = reduxBulletins;
  const loading = reduxLoading;

  const [activeTab, setActiveTab] = useState(() => {
    return (
      parseInt(localStorage.getItem("bulletinActiveTab")) ||
      location.state?.activeTab ||
      1
    );
  });
  const [myPostChecked, setMyPostChecked] = useState(false);
  const [selectedPriority, setSelectedPriority] = useState([]);
  const [selectedLabel, setSelectedLabel] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [availableLabels, setAvailableLabels] = useState([]);
  const [selectedImages, setSelectedImages] = useState([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isImageSliderOpen, setIsImageSliderOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [isDocumentViewerOpen, setIsDocumentViewerOpen] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [bulletinToDelete, setBulletinToDelete] = useState(null);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [selectedBulletinForHistory, setSelectedBulletinForHistory] = useState(null);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const [selectedDate, setSelectedDate] = useState("");
  const [openDropdownId, setOpenDropdownId] = useState(null);
  const dropdownRef = useRef(null);

  // Update available labels based on all bulletins (not filtered by tab)
  useEffect(() => {
    if (bulletins && bulletins.length > 0) {
      // Extract unique labels from ALL bulletins regardless of tab
      // Split comma-separated labels into individual labels
      const allLabels = bulletins
        .map((bulletin) => bulletin.label)
        .filter((label) => label && label.trim() !== "")
        .flatMap((label) =>
          label.split(',').map(l => l.trim()).filter(l => l !== "")
        );

      const uniqueLabels = [...new Set(allLabels)];
      setAvailableLabels(uniqueLabels);
    } else {
      setAvailableLabels([]);
    }
  }, [bulletins]);

  const [pinErrorMessage, setPinErrorMessage] = useState("");
  const [showPinError, setShowPinError] = useState(false);

  const pinPost = usePinPost({
    bulletins: bulletins,
    setBulletins: () => {},
    onPinSuccess: (message) => {
      console.log("Pin success:", message);
      loadBulletins(); // Refresh to get updated pin counts
    },
    onPinError: (message) => {
      console.error("Pin error:", message);
      setPinErrorMessage(message);
      setShowPinError(true);
      // Auto-hide error after 5 seconds
      setTimeout(() => {
        setShowPinError(false);
        setPinErrorMessage("");
      }, 5000);
    },
    currentTab: activeTab,
    onMoveToArchive: (bulletinId) => {
      console.log("Pin moved to archive:", bulletinId);
      loadBulletins(); // Refresh the bulletins list
      // Switch to archive tab to show the moved bulletin
      setActiveTab(3);
      localStorage.setItem("bulletinActiveTab", "3");
    }
  });

  useEffect(() => {
    loadBulletins();
  }, []);

  useEffect(() => {
    loadBulletins();
  }, [myPostChecked, selectedPriority, selectedLabel, searchTerm]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpenDropdownId(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    if (location.state?.activeTab) {
      setActiveTab(location.state.activeTab);
      window.history.replaceState({}, document.title);
    }

    if (location.state?.bulletinId) {
      setTimeout(() => {
        loadBulletins(true);
      }, 100);
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const loadBulletins = async (forceRefresh = false) => {
    try {
      const accessToken = localStorage.getItem("access_token");
      if (!accessToken) {
        console.error("No access token found. User needs to login.");
        return;
      }

      const params = {};
      if (forceRefresh) params._t = Date.now();
      if (selectedPriority.length > 0) params.priority = selectedPriority.map(p => p.toLowerCase()).join(',');
      if (selectedLabel.length > 0) params.labels = selectedLabel.join(',');
      if (searchTerm) params.search = searchTerm;

      const result = await loadBulletinsRedux(params);
      if (result.error) {
        console.error("Error loading bulletins:", result.error);
      }
    } catch (error) {
      console.error("Error loading bulletins:", error);
      if (error.message?.includes("401") || error.message?.includes("unauthorized")) {
        localStorage.removeItem("access_token");
        localStorage.removeItem("refresh_token");
      }
    }
  };

  const getFilteredBulletins = () => {
    let filtered = bulletins;

    if (activeTab === 1) {
      filtered = filtered.filter((bulletin) => bulletin.status === "current");
    } else if (activeTab === 2) {
      filtered = filtered.filter((bulletin) => bulletin.status === "pending");
    } else if (activeTab === 3) {
      filtered = filtered.filter((bulletin) => bulletin.status === "archive");
    }

    if (searchTerm) {
      filtered = filtered.filter(
        (bulletin) =>
          bulletin.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          bulletin.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          bulletin.author.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (selectedDate) {
      filtered = filtered.filter((bulletin) => {
        if (!bulletin.createdAt) return false;
        const selectedDateObj = new Date(selectedDate);
        selectedDateObj.setHours(0, 0, 0, 0);
        const bulletinDate = new Date(bulletin.createdAt);
        bulletinDate.setHours(0, 0, 0, 0);
        return selectedDateObj.getTime() === bulletinDate.getTime();
      });
    }

    if (selectedPriority.length > 0) {
      filtered = filtered.filter(
        (bulletin) =>
          bulletin.priority &&
          selectedPriority.some(priority =>
            bulletin.priority.toLowerCase() === priority.toLowerCase()
          )
      );
    }

    if (selectedLabel.length > 0) {
      filtered = filtered.filter(
        (bulletin) => {
          if (!bulletin.label) return false;
          // Split the bulletin's label by comma and check if any matches selected labels
          const bulletinLabels = bulletin.label.split(',').map(l => l.trim().toLowerCase());
          return selectedLabel.some(selectedLbl =>
            bulletinLabels.includes(selectedLbl.toLowerCase())
          );
        }
      );
    }

    if (myPostChecked) {
      const member = localStorage.getItem("member");
      let currentUserName = null;
      if (member) {
        try {
          const currentUser = JSON.parse(member);
          currentUserName =
            currentUser.full_name || currentUser.fullName || currentUser.name;
        } catch (error) {
          console.error("Error parsing member data:", error);
        }
      }
      if (currentUserName) {
        filtered = filtered.filter(
          (bulletin) =>
            bulletin.creatorName &&
            bulletin.creatorName.toLowerCase() === currentUserName.toLowerCase()
        );
      } else {
        filtered = [];
      }
    }

    return pinPost.sortAnnouncementsWithPinned(filtered);
  };

  const handleCreateBulletin = () => {
    navigate("/create-bulletin", {
      state: { sourceTab: activeTab }
    });
  };

  const handleEditBulletin = (bulletinId) => {
    navigate(`/edit-bulletin/${bulletinId}`, {
      state: {
        sourceTab: activeTab,
        bulletinId: bulletinId
      }
    });
  };

  // Handle delete bulletin
  const handleDeleteBulletin = (bulletinId) => {
    setBulletinToDelete(bulletinId);
    setShowDeleteConfirmation(true);
  };

  const confirmDeleteBulletin = async () => {
    if (bulletinToDelete) {
      try {
        await removeBulletinRedux(bulletinToDelete);
        loadBulletins();
      } catch (error) {
        console.error('Error deleting bulletin:', error);
      }
    }
    setShowDeleteConfirmation(false);
    setBulletinToDelete(null);
  };



  const handleBulletinHistory = async (bulletinId) => {
    try {
      await loadBulletinRedux(bulletinId);
      const bulletin = bulletins.find(
        (bull) => bull.id === bulletinId
      );
      if (bulletin) {
        setSelectedBulletinForHistory(bulletin);
        setShowHistoryModal(true);
      }
    } catch (error) {
      console.error("Error fetching bulletin for history:", error);
      const bulletin = bulletins.find(
        (bull) => bull.id === bulletinId
      );
      if (bulletin) {
        setSelectedBulletinForHistory(bulletin);
        setShowHistoryModal(true);
      }
    }
  };

  const handleReminder = (bulletinId) => {
    console.log("Set reminder for bulletin:", bulletinId);
  };

  const handlePinPost = pinPost.handlePinPost;

  const handleMoveToArchive = async (bulletinId) => {
    try {
      await moveBulletinToArchiveRedux(bulletinId);
      loadBulletins();
    } catch (error) {
      console.error("Error moving bulletin to archive:", error);
    }
  };

  const handleDirectCommunication = (bulletinId) => {
    console.log("Start direct communication for bulletin:", bulletinId);
  };

  const handleRestoreBulletin = async (bulletinId) => {
    try {
      await restoreArchivedBulletinRedux(bulletinId);
      loadBulletins();
    } catch (error) {
      console.error("Error restoring bulletin:", error);
    }
  };

  const handleClearSuccessMessage = () => {
    clearAllSuccessRedux();
  };

  const isImage = (fileName) => {
    const extension = fileName?.split(".").pop()?.toLowerCase();
    return ["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(extension);
  };

  const handleImageClick = (attachment, bulletin) => {
    const allAttachments = bulletin.attachments || [];
    const imageAttachments = allAttachments.filter(
      (att) =>
        isImage(att.file_name || att.name) ||
        (!att.file_name && !att.name && !isDocument(att.file_name || att.name))
    );
    const clickedIndex = imageAttachments.findIndex(
      (img) =>
        (img.file_url || img.url || img) ===
        (attachment.file_url || attachment.url || attachment)
    );
    const formattedImages = imageAttachments.map((img, index) => ({
      src: img.file_url || img.url || img,
      alt: img.file_name || img.name || `Image ${index + 1}`,
      name: img.file_name || img.name || `image-${index + 1}`
    }));
    setSelectedImages(formattedImages);
    setSelectedImageIndex(clickedIndex >= 0 ? clickedIndex : 0);
    setIsImageSliderOpen(true);
  };

  const handleImageSliderClose = () => {
    setIsImageSliderOpen(false);
    setSelectedImages([]);
    setSelectedImageIndex(0);
  };

  const handleDocumentClick = (attachment) => {
    setSelectedDocument(attachment);
    setIsDocumentViewerOpen(true);
  };

  const handleDocumentClose = () => {
    setIsDocumentViewerOpen(false);
    setSelectedDocument(null);
  };

  const getFileIcon = (fileName) => {
    const extension = fileName?.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "pdf":
        return <FaFilePdf className="w-6 h-6 text-black font-bold" />;
      case "doc":
      case "docx":
        return <FaFileWord className="w-6 h-6 text-blue-500" />;
      default:
        return <FaImage className="w-6 h-6 text-gray-400" />;
    }
  };

  const isDocument = (fileName) => {
    const extension = fileName?.split(".").pop()?.toLowerCase();
    return ["pdf", "doc", "docx"].includes(extension);
  };

  const handleHistoryModalClose = () => {
    setShowHistoryModal(false);
    setSelectedBulletinForHistory(null);
  };

  const handleRefreshBulletin = async (bulletinId) => {
    try {
      // Reload the specific bulletin to get updated history
      const result = await loadBulletinRedux(bulletinId);

      // If the load was successful, update the selected bulletin
      if (result && result.payload) {
        setSelectedBulletinForHistory(result.payload);
      } else {
        // Fallback: refresh the bulletins list and find the updated bulletin
        await loadBulletinsRedux();
        const refreshedBulletin = bulletins.find(bull => bull.id === bulletinId);
        if (refreshedBulletin) {
          setSelectedBulletinForHistory(refreshedBulletin);
        }
      }
    } catch (error) {
      console.error("Error refreshing bulletin:", error);
    }
  };

  const handleDropdownToggle = (bulletinId) => {
    setOpenDropdownId(
      openDropdownId === bulletinId ? null : bulletinId
    );
  };

  const handleFilterToggle = () => {
    setIsFilterExpanded(!isFilterExpanded);
  };

  const handleTabChange = (tabNumber) => {
    setActiveTab(tabNumber);
    localStorage.setItem("bulletinActiveTab", tabNumber.toString());
  };





  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header */}
      <div className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-900">
              Bulletins List
            </h1>

            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={myPostChecked}
                  onChange={(e) => setMyPostChecked(e.target.checked)}
                  className="form-checkbox h-4 w-4 text-primary rounded border-gray-300 focus:ring-primary"
                />
                <span className="ml-2 text-sm text-primary">My Post</span>
              </label>

              <button
                onClick={handleFilterToggle}
                className={`flex items-center justify-center  px-4 py-2 rounded-lg transition-colors text-sm font-medium w-[99px] h-[48px] ${
                  isFilterExpanded
                    ? "bg-primary text-white"
                    : "bg-white text-primary border border-primary hover:bg-gray-50"
                }`}
              >
                <BiFilter className="mr-2 w-4 h-4" />
                Filter
              </button>

              <button
                onClick={handleCreateBulletin}
                className="flex items-center justify-center bg-primary text-white px-4 py-2 rounded-lg transition-colors text-sm font-medium w-[217px] h-[48px]"
              >
                <FaPlus className="mr-2 w-4 h-4" />
                Create Bulletins
              </button>
            </div>
          </div>

          {isFilterExpanded && (
            <div className="flex items-center justify-end space-x-6">
              <div className="flex items-center justify-end space-x-6">
                <div className="min-w-[160px]">
                  <Calendar
                    value={selectedDate}
                    onChange={setSelectedDate}
                    placeholder="Select Date"
                  />
                </div>

                

                <div className="min-w-[160px]">
                  <FilterSelectModal
                    placeholder="Select Label"
                    options={availableLabels.map(label => ({ value: label, label: label }))}
                    value={selectedLabel}
                    onApply={setSelectedLabel}
                    className="w-full"
                  />
                </div>

                <div className="relative min-w-[200px] max-w-[250px]">
                  <BiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search list..."
                    className="w-full h-[42px] pl-10 pr-4 border border-primary rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm text-primary placeholder-primary"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>


            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="rounded-[27px] bg-white">
        <div className="p-4">
          <div className="flex mb-4 bg-[#3C9D9B1A] rounded">
            <button
              className={`flex-1 w-full py-2 font-[600] text-[#090909] ${
                activeTab === 1
                  ? "border border-primary"
                  : "border border-transparent"
              }`}
              onClick={() => handleTabChange(1)}
            >
              Current Bulletin
            </button>
            <button
              className={`flex-1 w-full py-2 font-[600] text-[#090909] ${
                activeTab === 2
                  ? "border border-primary"
                  : "border border-transparent"
              }`}
              onClick={() => handleTabChange(2)}
            >
              Pending Bulletin
            </button>
            <button
              className={`flex-1 w-full py-2 font-[600] text-[#090909] ${
                activeTab === 3
                  ? "border border-primary"
                  : "border border-transparent"
              }`}
              onClick={() => handleTabChange(3)}
            >
              Archive Bulletin
            </button>
          </div>

          {/* Bulletins List */}
          {getFilteredBulletins().length === 0 ? (
            <div className="flex flex-col justify-center items-center min-h-[300px] text-center w-full">
              <h3 className="text-xl font-semibold text-gray-900">
                No bulletins found
              </h3>
            </div>
          ) : activeTab === 2 ? (
            // Table view for pending bulletins
            <BulletinTableView
              bulletins={getFilteredBulletins()}
              loading={loading}
              handleBulletinHistory={handleBulletinHistory}
            />
          ) : (
            // Card view for current and archive bulletins
            <BulletinListPreview
              bulletins={getFilteredBulletins()}
              loading={loading}
              openDropdownId={openDropdownId}
              dropdownRef={dropdownRef}
              currentTab={activeTab}
              handleDropdownToggle={handleDropdownToggle}
              handleEditBulletin={handleEditBulletin}
              handleBulletinHistory={handleBulletinHistory}
              handleMoveToArchive={handleMoveToArchive}
              handleReminder={handleReminder}
              handlePinPost={handlePinPost}
              handlePinIconClick={pinPost.handlePinIconClick}
              handleDirectCommunication={handleDirectCommunication}
              handleDeleteBulletin={handleDeleteBulletin}
              handleRestoreBulletin={handleRestoreBulletin}
              handleImageClick={handleImageClick}
              handleDocumentClick={handleDocumentClick}
              isDocument={isDocument}
              getFileIcon={getFileIcon}
            />
          )}
        </div>
      </div>



      <ImageSlider
        isOpen={isImageSliderOpen}
        onClose={handleImageSliderClose}
        images={selectedImages}
        initialIndex={selectedImageIndex}
      />

      {showDeleteConfirmation && (
        <ConfirmationMessageBox
          message="Are you sure you want to delete this bulletin? This action cannot be undone."
          onConfirm={confirmDeleteBulletin}
          onCancel={() => setShowDeleteConfirmation(false)}
        />
      )}

      {deleteSuccess && message && (
        <MessageBox
          message={message}
          clearMessage={handleClearSuccessMessage}
        />
      )}

      {showPinError && pinErrorMessage && (
        <MessageBox
          message={pinErrorMessage}
          clearMessage={() => {
            setShowPinError(false);
            setPinErrorMessage("");
          }}
          type="error"
        />
      )}

      {showHistoryModal && selectedBulletinForHistory && (
        <BulletinHistoryModal
          bulletin={selectedBulletinForHistory}
          onClose={handleHistoryModalClose}
          onRefresh={handleRefreshBulletin}
        />
      )}

      {isDocumentViewerOpen && selectedDocument && (
        <DocumentViewer
          fileUrl={
            selectedDocument.file_url ||
            selectedDocument.url ||
            selectedDocument.base64
          }
          fileName={selectedDocument.file_name || selectedDocument.name}
          onClose={handleDocumentClose}
        />
      )}
    </div>
  );
};

export default BulletinList;
