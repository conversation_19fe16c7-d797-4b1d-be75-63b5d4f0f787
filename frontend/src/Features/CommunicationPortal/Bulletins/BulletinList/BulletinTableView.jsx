import React from 'react';
import { FaEye } from 'react-icons/fa';
import { HiUserCircle } from "react-icons/hi";
import { FaUserGroup } from "react-icons/fa6";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";

/**
 * BulletinTableView Component
 * Table-style view for pending bulletins matching the design in the provided image
 */
const BulletinTableView = ({
  bulletins,
  loading,
  handleBulletinHistory
}) => {
  if (loading) {
    return (
      <div className="col-span-full flex justify-center items-center py-12">
        <LoadingAnimation />
      </div>
    );
  }

  // Format date and time
  const formatDateTime = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    const period = hours >= 12 ? 'pm' : 'am';
    return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
  };

  // Get tower and unit info
  const getTowerUnitInfo = (bulletin) => {
    if (bulletin.target_towers_data && bulletin.target_towers_data.length > 0) {
      // Show all towers if multiple, otherwise show the single tower
      const towerNames = bulletin.target_towers_data.map(tower =>
        tower.tower_name || tower.name || `Tower ${tower.id}`
      );
      const towerDisplay = towerNames.length > 1
        ? `${towerNames.slice(0, 2).join(', ')}${towerNames.length > 2 ? ` +${towerNames.length - 2} more` : ''}`
        : towerNames[0];

      const unit = bulletin.target_units_data && bulletin.target_units_data.length > 0
        ? bulletin.target_units_data[0]
        : null;

      return {
        tower: towerDisplay,
        unit: unit ? unit.unit_name || unit.unit_number || unit.id : ''
      };
    }
    return { tower: '', unit: '' };
  };

  // Get display name and icon based on post_as field
  const getDisplayInfo = (bulletin) => {
    switch (bulletin.post_as) {
      case 'group':
        return {
          name: bulletin.group_name || 'Unknown Group',
          icon: <FaUserGroup className="w-6 h-6 text-gray-400" />
        };
      case 'member':
        return {
          name: bulletin.member_name || 'Unknown Member',
          icon: <HiUserCircle className="w-6 h-6 text-gray-400" />
        };
      default: // 'creator' or any other value
        return {
          name: bulletin.creator_name || 'Unknown User',
          icon: <HiUserCircle className="w-6 h-6 text-gray-400" />
        };
    }
  };

  return (
    <div className="w-full">
      {/* Table Header - Always visible with EBF5F5 background */}
      <div className="grid grid-cols-4 gap-4 p-4 border-b border-gray-200 text-sm font-semibold text-gray-700 sticky top-0 z-10" style={{ backgroundColor: '#EBF5F5' }}>
        <div>Name</div>
        <div>Bulletin Title</div>
        <div>Date & Time</div>
        <div>Actions</div>
      </div>

      {/* Table Body */}
      <div className="divide-y divide-gray-200">
        {bulletins.map((bulletin) => {
          const { name, icon } = getDisplayInfo(bulletin);

          return (
            <div key={bulletin.id} className="grid grid-cols-4 gap-4 p-4 items-center hover:bg-gray-50 transition-colors bg-white">
              {/* Name - Shows based on post_as field */}
              <div className="flex items-center space-x-3">
                {icon}
                <span className="text-sm text-gray-900">
                  {name}
                </span>
              </div>

              {/* Bulletin Title */}
              <div className="text-sm text-gray-900">
                <div className="line-clamp-2" title={bulletin.title}>
                  {bulletin.title}
                </div>
              </div>


              {/* Date & Time - Shows when bulletin was created */}
              <div className="text-sm text-gray-900">
                {formatDateTime(bulletin.created_at || bulletin.createdAt)}
              </div>

              {/* Actions */}
              <div className="flex items-center">
                <button
                  onClick={() => handleBulletinHistory(bulletin.id)}
                  className="p-2 text-primary hover:text-primary/80 transition-colors"
                  title="View History"
                >
                  <FaEye className="w-4 h-4" />
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {bulletins.length === 0 && (
        <div className="flex flex-col justify-center items-center min-h-[300px] text-center w-full bg-white">
          <h3 className="text-xl font-semibold text-gray-900">
            No pending bulletins found
          </h3>
        </div>
      )}
    </div>
  );
};

export default BulletinTableView;
