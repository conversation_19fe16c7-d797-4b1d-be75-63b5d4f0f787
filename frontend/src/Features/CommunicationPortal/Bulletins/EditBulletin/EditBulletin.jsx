import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ArrowLeft } from 'lucide-react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import BulletinPreview from '../components/BulletinPreview';
import EditBulletinForm from './EditBulletinForm';
import MessageBox from '../../../../Components/MessageBox/MessageBox';
import LoadingAnimation from '../../../../Components/Loaders/LoadingAnimation';
import useCurrentUser from '../hooks/useCurrentUser';
import { useBulletinEdit } from "../../../../hooks/useBulletins";
import { formatBulletinForEdit, formatBulletinForApi } from "../utils/bulletinUtils";

// Emoji validation function
const containsEmoji = (text) => {
  if (!text) return false;
  // Regex to detect emojis including various Unicode ranges
  const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F018}-\u{1F270}]|[\u{238C}-\u{2454}]|[\u{20D0}-\u{20FF}]/u;
  return emojiRegex.test(text);
};

// Validation schema (without date/time fields)
const bulletinSchema = yup.object().shape({
  title: yup
    .string()
    .required('Title is required')
    .test('no-emoji', 'Emojis are not allowed in title', (value) => {
      if (!value) return true;
      return !containsEmoji(value);
    })
    .test('word-count', 'Title must be 10 words or less', (value) => {
      if (!value) return true;
      return value.trim().split(/\s+/).length <= 10;
    }),
  description: yup
    .string()
    .test('no-emoji', 'Emojis are not allowed in description', (value) => {
      if (!value) return true;
      return !containsEmoji(value);
    })
    .test('word-count', 'Description must be 100 words or less', (value) => {
      if (!value) return true;
      return value.trim().split(/\s+/).length <= 100;
    }),
  postAs: yup.string().required('Post as selection is required'),
  creatorName: yup.string().required('Creator name is required'),
  selectedMemberId: yup.string().when('postAs', {
    is: 'Member',
    then: (schema) => schema.required('Please select a member'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedMemberName: yup.string().when('postAs', {
    is: 'Member',
    then: (schema) => schema.required('Please select a member'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedGroupId: yup.string().when('postAs', {
    is: 'Group',
    then: (schema) => schema.required('Please select a group'),
    otherwise: (schema) => schema.notRequired()
  }),
  selectedGroupName: yup.string().when('postAs', {
    is: 'Group',
    then: (schema) => schema.required('Please select a group'),
    otherwise: (schema) => schema.notRequired()
  }),
  priority: yup
    .string()
    .required('Priority is required')
    .oneOf(['low', 'normal', 'high', 'urgent'], 'Invalid priority value'),
  label: yup.string().required('Label is required'),
  selectedTowers: yup.array().notRequired(),
  selectedUnits: yup.array().notRequired(),
  attachments: yup.array()
});

/**
 * EditBulletin Component
 * Main component for editing bulletins with layout and state management
 */
const EditBulletin = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { id } = useParams();
  const { currentUser, manualRefresh } = useCurrentUser();
  
  // Use bulletin edit hook
  const {
    bulletin,
    loading,
    updating,
    updateError,
    updateSuccess,
    updateBulletin: updateBulletinAction,
    resetUpdate
  } = useBulletinEdit(id);

  const [attachments, setAttachments] = useState([]);
  const [attachmentsToDelete, setAttachmentsToDelete] = useState([]);
  const [successMessage, setSuccessMessage] = useState("");
  const [fileUploadError, setFileUploadError] = useState("");
  const [apiError, setApiError] = useState("");
  const [towerError, setTowerError] = useState("");
  const [unitError, setUnitError] = useState("");
  const [formError, setFormError] = useState("");
  const [titleError, setTitleError] = useState("");
  const [descriptionError, setDescriptionError] = useState("");
  const [priorityError, setPriorityError] = useState("");
  const [labelError, setLabelError] = useState("");
  const [titleWordLimitError, setTitleWordLimitError] = useState("");
  const [descriptionWordLimitError, setDescriptionWordLimitError] = useState("");
  const [hasChanges, setHasChanges] = useState(false);
  const [originalData, setOriginalData] = useState(null);

  // Form setup with validation
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    trigger,
    reset,
    clearErrors
  } = useForm({
    resolver: yupResolver(bulletinSchema),
    defaultValues: {
      title: "",
      description: "",
      postAs: "Creator",
      creatorName: currentUser?.full_name || "",
      selectedMemberId: "",
      selectedMemberName: "",
      selectedGroupId: "",
      selectedGroupName: "",
      priority: "normal",
      label: "",
      selectedTowers: [],
      selectedUnits: [],
      attachments: []
    }
  });

  // Watch form values for preview and change detection
  const watchedValues = watch();
  const postAs = watch("postAs");
  const selectedTowers = watch("selectedTowers");

  // Load bulletin data when component mounts or bulletin changes
  useEffect(() => {
    if (bulletin && !loading) {
      const formattedData = formatBulletinForEdit(bulletin);
      
      // Set form values
      Object.keys(formattedData).forEach(key => {
        setValue(key, formattedData[key]);
      });
      
      // Set attachments
      setAttachments(bulletin.attachments || []);
      
      // Store original data for change detection
      setOriginalData(formattedData);
      
      // Reset form state
      setHasChanges(false);
      clearAllErrors();
    }
  }, [bulletin, loading, setValue]);

  // Watch for form changes
  useEffect(() => {
    if (originalData) {
      const currentData = {
        title: watch("title"),
        description: watch("description"),
        postAs: watch("postAs"),
        selectedMemberId: watch("selectedMemberId"),
        selectedGroupId: watch("selectedGroupId"),
        priority: watch("priority"),
        label: watch("label"),
        selectedTowers: watch("selectedTowers"),
        selectedUnits: watch("selectedUnits")
      };
      
      const hasFormChanges = JSON.stringify(currentData) !== JSON.stringify(originalData);
      const hasAttachmentChanges = attachmentsToDelete.length > 0 || 
                                  attachments.some(att => !att.id); // New attachments don't have ID
      
      setHasChanges(hasFormChanges || hasAttachmentChanges);
    }
  }, [watchedValues, attachments, attachmentsToDelete, originalData, watch]);

  // Handle update success
  useEffect(() => {
    if (updateSuccess) {
      setSuccessMessage("Bulletin updated successfully!");
      setHasChanges(false);
      setTimeout(() => {
        resetUpdate();
        navigate("/bulletins", { state: { activeTab: location.state?.activeTab || 1 } });
      }, 2000);
    }
  }, [updateSuccess, resetUpdate, navigate, location.state]);

  // Handle update error
  useEffect(() => {
    if (updateError) {
      setApiError(updateError.message || "Failed to update bulletin");
    }
  }, [updateError]);

  // Clear all error states
  const clearAllErrors = () => {
    setFileUploadError("");
    setApiError("");
    setTowerError("");
    setUnitError("");
    setFormError("");
    setTitleError("");
    setDescriptionError("");
    setPriorityError("");
    setLabelError("");
    setTitleWordLimitError("");
    setDescriptionWordLimitError("");
  };

  // Handle back navigation
  const handleBack = () => {
    if (hasChanges) {
      const confirmLeave = window.confirm("You have unsaved changes. Are you sure you want to leave?");
      if (!confirmLeave) return;
    }
    navigate("/bulletins", { state: { activeTab: location.state?.activeTab || 1 } });
  };

  // Handle title change with word count validation
  const handleTitleChange = (e) => {
    const value = e.target.value;
    setValue("title", value);
    
    // Clear previous errors
    setTitleError("");
    setTitleWordLimitError("");
    clearErrors("title");
    
    // Check for emojis
    if (containsEmoji(value)) {
      setTitleError("Emojis are not allowed in title");
      return;
    }
    
    // Check word count
    const wordCount = getTitleWordCount();
    if (wordCount > 10) {
      setTitleWordLimitError("Title must be 10 words or less");
    }
    
    trigger("title");
  };

  // Get title word count
  const getTitleWordCount = () => {
    const title = watch("title") || "";
    return title.trim() === "" ? 0 : title.trim().split(/\s+/).length;
  };

  // Handle description change with word count validation
  const handleDescriptionChange = (e) => {
    const value = e.target.value;
    setValue("description", value);
    
    // Clear previous errors
    setDescriptionError("");
    setDescriptionWordLimitError("");
    clearErrors("description");
    
    // Check for emojis
    if (containsEmoji(value)) {
      setDescriptionError("Emojis are not allowed in description");
      return;
    }
    
    // Check word count
    const wordCount = getDescriptionWordCount();
    if (wordCount > 100) {
      setDescriptionWordLimitError("Description must be 100 words or less");
    }
    
    trigger("description");
  };

  // Get description word count
  const getDescriptionWordCount = () => {
    const description = watch("description") || "";
    return description.trim() === "" ? 0 : description.trim().split(/\s+/).length;
  };

  // Handle file upload
  const handleFileUpload = (files) => {
    setFileUploadError("");

    // Validate files
    const validFiles = [];
    const errors = [];

    Array.from(files).forEach(file => {
      // Check file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        errors.push(`${file.name} is too large (max 10MB)`);
        return;
      }

      // Check file type
      const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff', 'image/webp',
        'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];

      if (!allowedTypes.includes(file.type)) {
        errors.push(`${file.name} is not a supported file type`);
        return;
      }

      validFiles.push(file);
    });

    if (errors.length > 0) {
      setFileUploadError(errors.join(', '));
      return;
    }

    setAttachments(prev => [...prev, ...validFiles]);
  };

  // Remove attachment
  const removeAttachment = (index, attachmentId = null) => {
    if (attachmentId) {
      // Mark existing attachment for deletion
      setAttachmentsToDelete(prev => [...prev, attachmentId]);
    }
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  // Handle member selection
  const handleMemberSelect = (member) => {
    setValue("selectedMemberId", member.id);
    setValue("selectedMemberName", member.full_name);
    trigger(["selectedMemberId", "selectedMemberName"]);
  };

  // Handle group selection
  const handleGroupSelect = (group) => {
    setValue("selectedGroupId", group.id);
    setValue("selectedGroupName", group.name);
    trigger(["selectedGroupId", "selectedGroupName"]);
  };

  // Save post as preference
  const savePostAsPreference = (postAsValue) => {
    localStorage.setItem("bulletinPostAsPreference", postAsValue);
  };

  // Check if form is valid
  const isFormValid = () => {
    const hasTitle = watch("title")?.trim();
    const hasDescription = watch("description")?.trim();
    const hasPriority = watch("priority");
    const hasLabel = watch("label")?.trim();
    const hasTowers = watch("selectedTowers")?.length > 0;
    const hasUnits = watch("selectedUnits")?.length > 0;

    // Check for validation errors
    const hasErrors = titleError || descriptionError || priorityError || labelError ||
                     titleWordLimitError || descriptionWordLimitError ||
                     towerError || unitError || Object.keys(errors).length > 0;

    return hasTitle && hasDescription && hasPriority && hasLabel &&
           (hasTowers || hasUnits) && !hasErrors;
  };

  // Handle form submission
  const onSubmit = async (data) => {
    try {
      clearAllErrors();

      // Validate form
      if (!isFormValid()) {
        setFormError("Please fill in all required fields correctly");
        return;
      }

      // Prepare bulletin data with attachments_to_delete for editing
      const bulletinDataWithDeletions = {
        ...data,
        attachments_to_delete: attachmentsToDelete
      };

      // Use the utility function to format data for API (this handles ID conversion properly)
      const formData = formatBulletinForApi(bulletinDataWithDeletions, attachments.filter(att => !att.id));

      // Update bulletin
      await updateBulletinAction(id, formData);

    } catch (error) {
      console.error("Error updating bulletin:", error);
      setApiError(error.response?.data?.message || "Failed to update bulletin");
    }
  };

  // Handle success message close
  const clearMessage = () => {
    setSuccessMessage("");
  };

  const handleSuccessOk = () => {
    navigate("/bulletins", { state: { activeTab: location.state?.activeTab || 1 } });
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingAnimation />
      </div>
    );
  }

  // Show error if bulletin not found
  if (!bulletin && !loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Bulletin Not Found</h2>
          <p className="text-gray-600 mb-4">The bulletin you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate("/bulletins")}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
          >
            Back to Bulletins
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-stroke">
      {/* Header */}
      <div className="shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <button
              onClick={handleBack}
              className="flex items-center text-gray-600 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              <span className="text-lg font-semibold">
                Edit Bulletins
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4">
          {/* Left Column - Preview */}
          <div className="order-2 lg:order-1 lg:w-full lg:col-span-4">
            <div className="bg-white rounded-lg shadow-sm p-6 lg:sticky lg:top-8 h-screen lg:h-[calc(100vh-6rem)] overflow-y-auto border-2">
              <BulletinPreview
                formData={watchedValues}
                attachments={attachments}
                currentUser={currentUser}
                isEditing={true}
              />
            </div>
          </div>

          {/* Right Column - Form (wider) */}
          <div className="order-1 lg:order-2 lg:col-span-8 bg-white rounded-lg shadow-sm p-6">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <EditBulletinForm
                register={register}
                errors={errors}
                watch={watch}
                setValue={setValue}
                trigger={trigger}
                attachments={attachments}
                attachmentsToDelete={attachmentsToDelete}
                currentUser={currentUser}
                bulletin={bulletin}
                // Error states
                fileUploadError={fileUploadError}
                towerError={towerError}
                unitError={unitError}
                formError={formError}
                titleError={titleError}
                descriptionError={descriptionError}
                priorityError={priorityError}
                labelError={labelError}
                titleWordLimitError={titleWordLimitError}
                descriptionWordLimitError={descriptionWordLimitError}
                apiError={apiError}
                // Handlers
                handleTitleChange={handleTitleChange}
                getTitleWordCount={getTitleWordCount}
                handleDescriptionChange={handleDescriptionChange}
                getDescriptionWordCount={getDescriptionWordCount}
                handleFileUpload={handleFileUpload}
                removeAttachment={removeAttachment}
                handleMemberSelect={handleMemberSelect}
                handleGroupSelect={handleGroupSelect}
                savePostAsPreference={savePostAsPreference}
                isFormValid={isFormValid}
                hasChanges={hasChanges}
                updating={updating}
                // Watched values
                postAs={postAs}
                selectedTowers={selectedTowers}
              />
            </form>
          </div>
        </div>
      </div>

      {/* Success Message Box */}
      <MessageBox
        message={successMessage}
        clearMessage={clearMessage}
        onOk={handleSuccessOk}
      />
    </div>
  );
};

export default EditBulletin;
