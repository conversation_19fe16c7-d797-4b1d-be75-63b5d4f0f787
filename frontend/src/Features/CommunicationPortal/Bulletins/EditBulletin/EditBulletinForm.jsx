import React from "react";
import { FaUpload, FaTimes } from "react-icons/fa";
import ErrorMessage from "../../../../Components/MessageBox/ErrorMessage";
import PostAsSelector from "../components/PostAsSelector";
import PriorityDropdown from "../components/PriorityDropdown";
import BulletinLabelSelector from "../components/BulletinLabelSelector";
import TowerUnitSelector from "../components/TowerUnitSelector";
import FileUpload from "../components/FileUpload";

/**
 * EditBulletinForm Component
 * Form component for editing bulletins (without date/time fields)
 */
const EditBulletinForm = ({
  register,
  errors,
  watch,
  setValue,
  trigger,
  attachments,
  attachmentsToDelete,
  currentUser,
  bulletin,
  // Error states
  fileUploadError,
  towerError,
  unitError,
  formError,
  titleError,
  descriptionError,
  priorityError,
  labelError,
  titleWordLimitError,
  descriptionWordLimitError,
  apiError,
  // Handlers
  handleTitleChange,
  getTitleWordCount,
  handleDescriptionChange,
  getDescriptionWordCount,
  handleFileUpload,
  removeAttachment,
  handleMemberSelect,
  handleGroupSelect,
  savePostAsPreference,
  isFormValid,
  hasChanges,
  updating,
  // Watched values
  postAs,
  selectedTowers
}) => {
  return (
    <div className="space-y-6">
      {/* Title Field */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Title <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <input
            type="text"
            {...register("title")}
            onChange={handleTitleChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.title || titleError || titleWordLimitError
                ? "border-red-500"
                : "border-gray-300"
            }`}
            placeholder="Enter bulletin title..."
          />
          <div className="absolute right-3 top-2 text-xs text-gray-500">
            {getTitleWordCount()}/10 words
          </div>
        </div>
        {(errors.title || titleError || titleWordLimitError) && (
          <ErrorMessage 
            message={errors.title?.message || titleError || titleWordLimitError} 
          />
        )}
      </div>

      {/* Description Field */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <textarea
            {...register("description")}
            onChange={handleDescriptionChange}
            rows={4}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
              errors.description || descriptionError || descriptionWordLimitError
                ? "border-red-500"
                : "border-gray-300"
            }`}
            placeholder="Enter bulletin description..."
          />
          <div className="absolute right-3 bottom-2 text-xs text-gray-500">
            {getDescriptionWordCount()}/100 words
          </div>
        </div>
        {(errors.description || descriptionError || descriptionWordLimitError) && (
          <ErrorMessage 
            message={errors.description?.message || descriptionError || descriptionWordLimitError} 
          />
        )}
      </div>

      {/* Post As Selector */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Post As <span className="text-red-500">*</span>
        </label>
        <PostAsSelector
          register={register}
          errors={errors}
          watch={watch}
          setValue={setValue}
          trigger={trigger}
          currentUser={currentUser}
          onMemberSelect={handleMemberSelect}
          onGroupSelect={handleGroupSelect}
          savePostAsPreference={savePostAsPreference}
          postAs={postAs}
          isEditing={true}
          bulletin={bulletin}
        />
      </div>

      {/* Priority Dropdown */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Priority <span className="text-red-500">*</span>
        </label>
        <PriorityDropdown
          register={register}
          errors={errors}
          watch={watch}
          setValue={setValue}
          priorityError={priorityError}
        />
      </div>

      {/* Label Selector */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Labels <span className="text-red-500">*</span>
        </label>
        <BulletinLabelSelector
          register={register}
          errors={errors}
          watch={watch}
          setValue={setValue}
          trigger={trigger}
          labelError={labelError}
          isEditing={true}
          bulletin={bulletin}
        />
      </div>

      {/* Tower and Unit Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Target Audience <span className="text-red-500">*</span>
        </label>
        <TowerUnitSelector
          register={register}
          errors={errors}
          watch={watch}
          setValue={setValue}
          trigger={trigger}
          towerError={towerError}
          unitError={unitError}
          selectedTowers={selectedTowers}
          isEditing={true}
          bulletin={bulletin}
        />
      </div>

      {/* File Upload */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Attachments
        </label>
        <FileUpload
          attachments={attachments}
          onFileUpload={handleFileUpload}
          onRemoveAttachment={removeAttachment}
          fileUploadError={fileUploadError}
          isEditing={true}
          attachmentsToDelete={attachmentsToDelete}
        />
      </div>

      {/* Status Information */}
      {bulletin && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Current Status</h4>
          <div className="flex items-center gap-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              bulletin.status === 'current' ? 'bg-green-100 text-green-800' :
              bulletin.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {bulletin.status.charAt(0).toUpperCase() + bulletin.status.slice(1)}
            </span>
            <span className="text-xs text-gray-500">
              Created {new Date(bulletin.created_at).toLocaleDateString()}
            </span>
          </div>
        </div>
      )}

      {/* Error Messages */}
      {formError && <ErrorMessage message={formError} />}
      {apiError && <ErrorMessage message={apiError} />}

      {/* Submit Button */}
      <div className="flex justify-end pt-6 border-t border-gray-200">
        <button
          type="submit"
          disabled={!isFormValid() || !hasChanges || updating}
          className={`px-6 py-3 rounded-lg font-medium transition-colors ${
            isFormValid() && hasChanges && !updating
              ? "bg-blue-600 hover:bg-blue-700 text-white"
              : "bg-gray-300 text-gray-500 cursor-not-allowed"
          }`}
        >
          {updating ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Updating...
            </div>
          ) : (
            "Update Bulletin"
          )}
        </button>
      </div>

      {/* Change Indicator */}
      {hasChanges && (
        <div className="text-xs text-blue-600 text-center">
          You have unsaved changes
        </div>
      )}
    </div>
  );
};

export default EditBulletinForm;
