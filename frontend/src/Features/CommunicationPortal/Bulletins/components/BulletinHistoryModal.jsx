import React, { useState } from 'react';
import { FaTimes } from 'react-icons/fa';
import useBulletins from '../../../../hooks/useBulletins';
import BulletinPreview from './BulletinPreview';
import useCurrentUser from '../hooks/useCurrentUser';

// Helper function to determine file type from filename
const getFileTypeFromName = (filename) => {
  if (!filename) return 'application/octet-stream';

  const extension = filename.toLowerCase().split('.').pop();
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const documentExtensions = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  };

  if (imageExtensions.includes(extension)) {
    return `image/${extension === 'jpg' ? 'jpeg' : extension}`;
  }

  return documentExtensions[extension] || 'application/octet-stream';
};

/**
 * BulletinHistoryModal Component
 * Enhanced history modal with comment section and Approve/Reject buttons
 * Matches the design shown in the provided images
 */
const BulletinHistoryModal = React.memo(({ bulletin, onClose, onRefresh }) => {
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [actionType, setActionType] = useState(null); // 'approve' or 'reject'
  const [localHistory, setLocalHistory] = useState([]); // Local history state
  const [isClosing, setIsClosing] = useState(false); // Track closing state

  const {
    approveBulletinPost,
    rejectBulletinPost,
    loadBulletins
  } = useBulletins();

  const { currentUser } = useCurrentUser();

  // Handle send comment (just add comment without approve/reject)
  const handleSendComment = async () => {
    if (!comment.trim()) return;

    setIsSubmitting(true);

    try {
      // Create new comment entry
      const newCommentEntry = {
        id: `comment-${Date.now()}`,
        type: 'Comment',
        action: 'comment',
        user: currentUser?.full_name || 'Current User',
        timestamp: new Date().toISOString(),
        date: (() => {
          const now = new Date();
          const day = now.getDate().toString().padStart(2, '0');
          const month = (now.getMonth() + 1).toString().padStart(2, '0');
          const year = now.getFullYear();
          const hours = now.getHours();
          const minutes = now.getMinutes().toString().padStart(2, '0');
          const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
          const period = hours >= 12 ? 'pm' : 'am';
          return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
        })(),
        comment: comment.trim(),
        changes: null
      };

      // Add to local history
      setLocalHistory(prev => [newCommentEntry, ...prev]);

      console.log('Comment added to history:', comment.trim());

      // Clear the comment
      setComment('');

    } catch (error) {
      console.error('Error sending comment:', error);
      alert('Failed to send comment');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Safe close handler to prevent shaking
  const handleSafeClose = () => {
    if (isSubmitting) {
      return; // Don't close while submitting
    }
    onClose();
  };

  // Handle approve action
  const handleApprove = async () => {
    // Prevent multiple clicks
    if (isSubmitting || isClosing) return;

    setIsSubmitting(true);
    setActionType('approve');

    try {
      // Perform the API call first
      await approveBulletinPost(bulletin.id, comment.trim() || '');

      // Start closing process immediately after API success
      setIsClosing(true);

      // Perform background updates without waiting
      Promise.all([
        onRefresh ? onRefresh(bulletin.id) : Promise.resolve(),
        loadBulletins()
      ]).catch(error => {
        console.error('Error refreshing data:', error);
      });

      // Close the modal with a slight delay for smooth transition
      setTimeout(() => {
        onClose();
      }, 150);

    } catch (error) {
      console.error('Error approving bulletin:', error);
      alert('Failed to approve bulletin');
      setIsClosing(false); // Reset closing state on error
    } finally {
      setIsSubmitting(false);
      setActionType(null);
    }
  };

  // Handle reject action
  const handleReject = async () => {
    // Prevent multiple clicks
    if (isSubmitting || isClosing) return;

    setIsSubmitting(true);
    setActionType('reject');

    try {
      // Perform the API call first
      await rejectBulletinPost(bulletin.id, comment.trim() || '');

      // Start closing process immediately after API success
      setIsClosing(true);

      // Perform background updates without waiting
      Promise.all([
        onRefresh ? onRefresh(bulletin.id) : Promise.resolve(),
        loadBulletins()
      ]).catch(error => {
        console.error('Error refreshing data:', error);
      });

      // Close the modal with a slight delay for smooth transition
      setTimeout(() => {
        onClose();
      }, 150);

    } catch (error) {
      console.error('Error rejecting bulletin:', error);
      alert('Failed to reject bulletin');
      setIsClosing(false); // Reset closing state on error
    } finally {
      setIsSubmitting(false);
      setActionType(null);
    }
  };

  // Get bulletin history (using same logic as announcement history)
  const getBulletinHistory = (bulletin) => {
    const history = [];

    // Add local history entries first (most recent)
    history.push(...localHistory);

    // Process editHistory (transformed format like announcements)
    if (bulletin.editHistory && Array.isArray(bulletin.editHistory)) {
      bulletin.editHistory.forEach((edit, index) => {
        const editDate = new Date(edit.timestamp);
        history.push({
          id: `edit-${edit.timestamp}-${index}`,
          type: 'Edit',
          action: 'Edited by',
          user: edit.editedBy || 'Unknown User',
          timestamp: edit.timestamp,
          date: (() => {
            const day = editDate.getDate().toString().padStart(2, '0');
            const month = (editDate.getMonth() + 1).toString().padStart(2, '0');
            const year = editDate.getFullYear();
            const hours = editDate.getHours();
            const minutes = editDate.getMinutes().toString().padStart(2, '0');
            const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
            const period = hours >= 12 ? 'pm' : 'am';
            return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
          })(),
          changes: edit.changes || {}
        });
      });
    }

    // Also check for backend history format (fallback)
    if (bulletin.history && Array.isArray(bulletin.history)) {
      bulletin.history.forEach((entry, index) => {
        const entryDate = new Date(entry.edited_at);

        // Add the main action entry (without comment)
        history.push({
          id: `history-${entry.id || index}`,
          type: entry.action,
          action: entry.action,
          user: entry.edited_by_name || 'Unknown User',
          timestamp: entry.edited_at,
          date: (() => {
            const day = entryDate.getDate().toString().padStart(2, '0');
            const month = (entryDate.getMonth() + 1).toString().padStart(2, '0');
            const year = entryDate.getFullYear();
            const hours = entryDate.getHours();
            const minutes = entryDate.getMinutes().toString().padStart(2, '0');
            const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
            const period = hours >= 12 ? 'pm' : 'am';
            return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
          })(),
          comment: null,
          changes: entry.changes
        });

        // Add separate comment entry if comment exists
        if (entry.comment && entry.comment.trim()) {
          history.push({
            id: `comment-${entry.id || index}`,
            type: 'Comment',
            action: 'comment',
            user: entry.edited_by_name || 'Unknown User',
            timestamp: entry.edited_at,
            date: (() => {
              const day = entryDate.getDate().toString().padStart(2, '0');
              const month = (entryDate.getMonth() + 1).toString().padStart(2, '0');
              const year = entryDate.getFullYear();
              const hours = entryDate.getHours();
              const minutes = entryDate.getMinutes().toString().padStart(2, '0');
              const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
              const period = hours >= 12 ? 'pm' : 'am';
              return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
            })(),
            comment: entry.comment,
            changes: null
          });
        }
      });
    }

    // Creation entry - add at the end (bottom of timeline)
    if (bulletin.createdAt || bulletin.created_at) {
      const createdDate = new Date(bulletin.createdAt || bulletin.created_at);
      history.push({
        id: `creation-${bulletin.id}`,
        type: 'Creation',
        action: 'Created by',
        user: bulletin.author || bulletin.creatorName || 'Unknown User',
        timestamp: bulletin.createdAt || bulletin.created_at,
        date: (() => {
          const day = createdDate.getDate().toString().padStart(2, '0');
          const month = (createdDate.getMonth() + 1).toString().padStart(2, '0');
          const year = createdDate.getFullYear();
          const hours = createdDate.getHours();
          const minutes = createdDate.getMinutes().toString().padStart(2, '0');
          const hour12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
          const period = hours >= 12 ? 'pm' : 'am';
          return `${day}-${month}-${year} at ${hour12}:${minutes}${period}`;
        })(),
        color: '#3D9D9B'
      });
    }

    return history.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)); // Show latest first (descending)
  };



  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleSafeClose();
    }
  };

  // Handle escape key
  React.useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        handleSafeClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [onClose]);

  // Use useMemo or recalculate history when localHistory changes
  // Don't recalculate if closing to prevent unnecessary re-renders
  const history = React.useMemo(() => {
    if (isClosing) return [];
    return getBulletinHistory(bulletin);
  }, [bulletin, localHistory, isClosing]);

  // Don't render if closing
  if (isClosing) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white relative flex overflow-hidden w-[796px] h-[660px] max-w-[90vw] max-h-[90vh] rounded-[27px] opacity-50 pointer-events-none transition-opacity duration-200">
          {/* Empty modal while closing */}
        </div>
      </div>
    );
  }

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div
        className={`bg-white relative flex w-[796px] h-[660px] max-w-[90vw] max-h-[90vh] rounded-[27px] transition-opacity duration-200 ${
          isClosing ? 'opacity-50 pointer-events-none' : 'opacity-100'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          onClick={handleSafeClose}
          className="absolute top-4 right-4 w-8 h-8 bg-primary hover:bg-[#2A7A78] rounded-full flex items-center justify-center text-white z-10 transition-colors"
        >
          <FaTimes className="w-4 h-4" />
        </button>

        {/* Left Side - Preview */}
        <div className="w-1/2 p-6 border-r-[3px] border-[#F9F9FB] overflow-y-auto">
          <div className="h-full">
            <BulletinPreview
              data={{
                title: bulletin.title,
                description: bulletin.description,
                authorName: bulletin.creator_name,
                postAs: bulletin.postAs || 'Creator',
                label: bulletin.label,
                attachments: bulletin.attachments ? bulletin.attachments.map(att => ({
                  preview: att.file_url || att.url || att.preview || att,
                  name: att.file_name || att.name || 'Attachment',
                  url: att.file_url || att.url || att.preview || att,
                  file_url: att.file_url || att.url || att.preview || att,
                  file_name: att.file_name || att.name || 'Attachment',
                  type: att.file_type || att.type || (att.file_name ? getFileTypeFromName(att.file_name) : 'application/octet-stream')
                })) : [],
                // Add selectedUnits for user count calculation
                selectedUnits: bulletin.target_units_data?.map(unit => unit.id) || [],
                // Also pass the full bulletin object for compatibility
                target_units_data: bulletin.target_units_data || []
              }}
              isInModal={true}
            />
          </div>
        </div>

        {/* Right Side - History */}
        <div className="w-1/2 p-6 overflow-y-auto">
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">History</h2>
          </div>

          {/* History Timeline */}
          <div className="relative">
            {/* Continuous Timeline Line */}
            {history.length > 1 && (
              <div
                className="absolute left-[6px] top-4 w-0.5 bg-primary"
                style={{ height: `${(history.length - 1) * 96}px` }}
              ></div>
            )}

            <div className="space-y-6">
              {history.length > 0 ? (
                history.map((entry) => (
                  <div key={entry.id} className="flex items-start space-x-4 relative">
                    {/* Timeline Dot */}
                    <div className="relative z-10">
                      <div className="w-3 h-3 bg-primary rounded-full mt-1"></div>
                    </div>

                    {/* History Entry */}
                    <div className="flex-1 pb-2">
                      {/* Header with Type and Date */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="text-base font-medium text-primary">
                          {entry.type}
                        </div>
                        <div className="text-sm text-gray-500">
                          {entry.date}
                        </div>
                      </div>

                      {/* User Info */}
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-700">{entry.action}</span>
                        <span className="text-sm font-medium text-primary bg-gray-100 px-2 py-1 rounded radius-8">
                          {entry.user}
                        </span>
                      </div>
                      </div>
                    </div>
                  )
                )
              ) : (
                <div className="text-gray-500 text-center py-4">No history available</div>
              )}
            </div>
          </div>

          {/* Comment Section for Pending Bulletins */}
          {bulletin.status === 'pending' && (
            <div className="border-t border-gray-200 pt-6">
              <div className="space-y-4">
                {/* Comment Input with User Avatar */}
                <div className="flex items-start space-x-3">
                  {/* User Avatar */}
                  <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <svg className="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                  </div>

                  {/* Comment Input */}
                  <div className="flex-1 flex items-end space-x-3">
                    <textarea
                      value={comment}
                      onChange={(e) => setComment(e.target.value)}
                      placeholder={`Comment as ${currentUser?.full_name || 'User'}`}
                      rows={2}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-none text-sm"
                      disabled={isSubmitting}
                      maxLength={500}
                    />
                    {/* Send Button - Positioned outside textarea */}
                    <button
                      onClick={handleSendComment}
                      className={`w-10 h-10 rounded-full flex items-center justify-center text-white transition-colors flex-shrink-0 ${
                        comment.trim() && !isSubmitting
                          ? 'bg-primary hover:bg-primary/90'
                          : 'bg-gray-300 cursor-not-allowed'
                      }`}
                      disabled={!comment.trim() || isSubmitting}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 mt-6">
                  <button
                    onClick={handleReject}
                    disabled={isSubmitting || isClosing}
                    className={`flex-1 px-6 py-3 rounded-lg font-medium text-base transition-colors ${
                      isSubmitting || isClosing
                        ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                        : 'bg-secondary text-white hover:bg-secondary/90'
                    }`}
                  >
                    {isSubmitting && actionType === 'reject' ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Rejecting...
                      </div>
                    ) : (
                      'Reject'
                    )}
                  </button>

                  <button
                    onClick={handleApprove}
                    disabled={isSubmitting || isClosing}
                    className={`flex-1 px-6 py-3 rounded-lg font-medium text-base transition-colors ${
                      isSubmitting || isClosing
                        ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                        : 'bg-primary text-white hover:bg-primary/90'
                    }`}
                  >
                    {isSubmitting && actionType === 'approve' ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Approving...
                      </div>
                    ) : (
                      'Approve'
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}

          {bulletin.status !== 'pending' && (
            <div className="text-center py-4">
              <p className="text-sm text-gray-500">
                {bulletin.status === 'current'
                  ? 'This bulletin is already approved and active'
                  : 'This bulletin is archived'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

BulletinHistoryModal.displayName = 'BulletinHistoryModal';

export default BulletinHistoryModal;

