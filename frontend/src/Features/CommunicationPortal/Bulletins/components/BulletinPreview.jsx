import React, { useState } from "react";
import { Flag, Image as ImageIcon } from "lucide-react";
import { VscFilePdf } from "react-icons/vsc";
import { FaFileWord } from "react-icons/fa";
import { HiUserCircle } from "react-icons/hi";
import { FaUserGroup } from "react-icons/fa6";
import { IoIosNotificationsOutline } from "react-icons/io";
import { useUserCount } from "../../Announcements/hooks/useUserCount";

/**
 * BulletinPreview Component
 * Real-time preview of the bulletin as it will appear when posted
 */
const BulletinPreview = ({ data, currentUser, isInModal = false }) => {
  const [expandedTitle, setExpandedTitle] = useState(false);

  // Use the custom hook for user count calculation
  // Handle both selectedUnits (from create/edit form) and target_units_data (from history/existing bulletins)
  const targetUnits =
    data.selectedUnits || data.target_units_data?.map((unit) => unit.id) || [];
  const { userCount, loading: loadingUserCount } = useUserCount(targetUnits);

  // Get display name based on post as selection
  const getDisplayName = () => {
    if (data.postAs === "Group" && data.selectedGroupName) {
      return data.selectedGroupName;
    } else if (data.postAs === "Member" && data.selectedMemberName) {
      return data.selectedMemberName;
    }
    return data.authorName || currentUser?.full_name || "Unknown User";
  };

  // Get author avatar based on post as selection
  const getAuthorAvatar = () => {
    if (data.postAs === "Creator" || data.postAs === "creator") {
      return <HiUserCircle className="w-8 h-8" color="gray" />;
    } else if (data.postAs === "Group" || data.postAs === "group") {
      return <FaUserGroup className="w-8 h-8" color="gray" />;
    } else if (data.postAs === "Member" || data.postAs === "member") {
      return <HiUserCircle className="w-8 h-8" color="gray" />;
    }
    return <HiUserCircle className="w-8 h-8" color="gray" />;
  };

  // Format labels for display
  const formatLabels = (labelString) => {
    if (!labelString) return [];
    return labelString
      .split(",")
      .map((label) => label.trim())
      .filter((label) => label.length > 0);
  };

  // Get formatted labels
  const labels = formatLabels(data.label);

  // Get current date and time for bulletin creation
  const getCurrentDateTime = () => {
    const now = new Date();
    const day = String(now.getDate()).padStart(2, '0');
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = now.getFullYear();

    let hours = now.getHours();
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'

    return `${day}-${month}-${year} at ${hours}:${minutes}${ampm}`;
  };

  // Handle attachment click
  const handleAttachmentClick = (attachment) => {
    if (attachment.type?.startsWith("image/")) {
      // Handle image click - could open in modal
      console.log("Image clicked:", attachment);
    } else {
      // Handle document click - could download or open
      console.log("Document clicked:", attachment);
    }
  };

  // Render attachment icon based on type
  const renderAttachmentIcon = (attachment) => {
    if (attachment.type?.startsWith("image/")) {
      return <ImageIcon className="w-4 h-4 text-blue-600" />;
    } else if (attachment.type === "application/pdf") {
      return <VscFilePdf className="w-4 h-4 text-red-600" />;
    } else if (
      attachment.type?.includes("word") ||
      attachment.type?.includes("document")
    ) {
      return <FaFileWord className="w-4 h-4 text-blue-600" />;
    }
    return <ImageIcon className="w-4 h-4 text-gray-600" />;
  };

  // Early return if no data
  if (!data) {
    return (
      <div className="text-center py-8">
        <IoIosNotificationsOutline className="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500">Start filling out the form to see a preview</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
     
      {/* Bulletin Card Preview */}
      <div className="border-[1px] border-primary rounded-lg overflow-hidden bg-white shadow-sm ">
        {/* Header with Author Info and Status */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-start justify-between">
            {/* Left side - Author Info */}
            <div className="flex items-center space-x-3">
              {/* Author Avatar and Info */}
              <div className="w-8 h-8 rounded-full flex items-center justify-center">
                {data.postAs === "Creator" || data.postAs === "creator" ? (
                  <HiUserCircle className="w-8 h-8" color="gray" />
                ) : data.postAs === "Group" || data.postAs === "group" ? (
                  <FaUserGroup className="w-8 h-8" color="gray" />
                ) : data.postAs === "Member" || data.postAs === "member" ? (
                  <FaUserGroup className="w-8 h-8" color="gray" />
                ) : (
                  <HiUserCircle className="w-8 h-8" color="gray" />
                )}
              </div>
              <div>
                <h3 className="text-sm font-bold text-gray-900">
                  {getDisplayName()}
                </h3>
                <div className="text-xs text-primary mt-1">
                  {getCurrentDateTime()}
                </div>
              </div>
            </div>

            {/* Right side - User count with notification icon */}
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                <IoIosNotificationsOutline className="w-5 h-5 text-gray-600" />
                <span className="text-sm font-medium text-gray-900">
                  {loadingUserCount ? "..." : userCount}
                </span>
              </div>
            </div>
          </div>

          {/* Label Info */}
          {labels.length > 0 && (
            <div className="mt-1 text-xs">
              <div className="flex flex-wrap gap-1">
                {labels.map((label, index) => (
                  <span
                    key={index}
                    className="bg-[#F5F5F5] text-[#090909] text-[10px] px-2 py-1 rounded font-bold"
                  >
                    {label}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Title */}
          <div className="mb-3">
            <h2
              className={`font-semibold text-gray-900 leading-tight ${
                expandedTitle ? "" : "line-clamp-2"
              }`}
            >
              {data.title || "Bulletin title will appear here..."}
            </h2>
            {data.title && data.title.length > 100 && (
              <button
                onClick={() => setExpandedTitle(!expandedTitle)}
                className="text-primary text-sm mt-1 hover:underline"
              >
                {expandedTitle ? "Show less" : "Show more"}
              </button>
            )}
          </div>

          {/* Description */}
          {data.description && (
            <div className="mb-4">
              <p className="text-gray-700 text-sm whitespace-pre-wrap">
                {data.description}
              </p>
            </div>
          )}



          {/* Attachments */}
          {data.attachments && data.attachments.length > 0 && (
            <div className="mb-4">
              <div className="grid grid-cols-2 gap-2">
                {data.attachments.map((attachment, index) => (
                  <div
                    key={index}
                    className="relative group cursor-pointer"
                    onClick={() => handleAttachmentClick(attachment)}
                  >
                    {attachment.type?.startsWith("image/") ? (
                      <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                        <img
                          src={attachment.preview || attachment.url}
                          alt={attachment.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.style.display = "none";
                            e.target.nextSibling.style.display = "flex";
                          }}
                        />
                        <div
                          className="w-full h-full hidden items-center justify-center bg-gray-100"
                          style={{ display: "none" }}
                        >
                          <ImageIcon className="w-8 h-8 text-gray-400" />
                        </div>
                      </div>
                    ) : (
                      <div className="aspect-square rounded-lg bg-gray-50 border-2 border-dashed border-gray-200 flex flex-col items-center justify-center p-4">
                        {renderAttachmentIcon(attachment)}
                        <span className="text-xs text-gray-600 mt-2 text-center truncate w-full">
                          {attachment.name}
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

      </div>
    </div>
  );
};

export default BulletinPreview;
